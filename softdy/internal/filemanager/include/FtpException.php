<?php
/*
 * This file is part of the `nicolab/php-ftp-client` package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 *
 * @copyright <PERSON> http://nicolab.net
 */
namespace FtpClient;

/**
 * The FtpException class.
 * Exception thrown if an error on runtime of the FTP client occurs.
 * @inheritDoc
 * <AUTHOR> <<EMAIL>>
 */
class FtpException extends \Exception {}
