<?php

return array(

	'Select' => '选择',
	'Erase' => '删除',
	'Open' => '打开',
	'Confirm_del' => '确定删除此文件?',
	'All' => '所有',
	'Files' => '文件',
	'Images' => '图片',
	'Archives' => '存档',
	'Error_Upload' => '上传的文件超过了允许的最大尺寸',
	'Error_extension' => '此类文件不被支持',
	'Upload_file' => '上传',
	'Filters' => '过滤',
	'Videos' => '视频',
	'Music' => '音乐',
	'New_Folder' => '新文件夹',
	'Folder_Created' => '文件夹创建成功',
	'Existing_Folder' => '文件夹已经存在',
	'Confirm_Folder_del' => '确定删除此文件夹和里面的所有文件?',
	'Return_Files_List' => '返回文件列表',
	'Preview' => '预览',
	'Download' => '下载',
	'Insert_Folder_Name' => '填写文件夹名称:',
	'Root' => 'root',
	'Rename' => '改名',
	'Back' => '返回',
	'View' => '视图',
	'View_list' => '列表视图',
	'View_columns_list' => '列视图',
	'View_boxes' => '方块视图',
	'Toolbar' => '工具栏',
	'Actions' => '操作',
	'Rename_existing_file' => '文件已存在',
	'Rename_existing_folder' => '文件夹已存在',
	'Empty_name' => '请输入文件名',
	'Text_filter' => '文字过滤',
	'Swipe_help' => '在文件或文件夹的名称上划过已显示更多选项',
	'Upload_base' => '普通上传',
	'Upload_url' => 'URL',
	'Upload_java' => 'JAVA上传(适用于大文件上传)',
	'Upload_java_help' => "如果Java Applet没有加载，你可以：1. 请确认你的电脑安装了Java。如果没有安装，在这里下载安装 <a href='http://java.com/en/download/'>[下载链接]</a>   2. 确保您电脑的防火墙没有阻挡Java上传",
	'Upload_base_help' => "将需要上传的文件拖动到以上区域 (需要比较新的浏览器才支持)，并选择相关文件。当上传完成，点击'返回文件列表'按钮",
	'Type_dir' => 'dir',
	'Type' => '类型',
	'Dimension' => '尺寸',
	'Size' => '大小',
	'Date' => '日期',
	'Filename' => '文件名',
	'Operations' => '操作',
	'Date_type' => 'y-m-d',
	'OK' => 'OK',
	'Cancel' => '取消',
	'Sorting' => '排序',
	'Show_url' => '显示URL',
	'Extract' => '解压缩到这里',
	'File_info' => '文件信息',
	'Edit_image' => '编辑图片',
	'Duplicate' => '复制',
	'Folders' => '文件夹',
	'Copy' => '拷贝',
	'Cut' => '剪切',
	'Paste' => '粘贴',
	'CB' => '粘贴板', // clipboard
	'Paste_Here' => '粘贴到这个目录',
	'Paste_Confirm' => '确定粘贴到这个目录? 这有可能会覆盖已经存在的文件或文件夹',
	'Paste_Failed' => '文件粘贴失败',
	'Clear_Clipboard' => '清除粘贴板',
	'Clear_Clipboard_Confirm' => '确定清除粘贴板?',
	'Files_ON_Clipboard' => '粘贴板上还有文件存在',
	'Copy_Cut_Size_Limit' => '无法 %s 选择的文件，选择的文件太大，超过了允许的大小: %d MB', // %s = cut or copy
	'Copy_Cut_Count_Limit' => '无法 %s 选择的文件，您选择的文件和文件夹数目超过限制: %d 个文件', // %s = cut or copy
	'Copy_Cut_Not_Allowed' => ' 您没有权限 %s 文件', // %s(1) = cut or copy, %s(2) = files or folders
	'Aviary_No_Save' => '无法保存图片',
	'Zip_No_Extract' => '文件解压缩失败。文件可能已经损坏',
	'Zip_Invalid' => '不支持此文件后缀，支持的后缀名: zip, gz, tar.',
	'Dir_No_Write' => '您选择的目录没有写权限',
	'Function_Disabled' => '%s 功能已经被服务器禁用。', // %s = cut or copy
	'File_Permission' => 'File permission',
	'File_Permission_Not_Allowed' => 'Changing %s permissions are not allowed.', // %s = files or folders
	'File_Permission_Recursive' => 'Apply recursively?',
	'File_Permission_Wrong_Mode' => "The supplied permission mode is incorrect.",
	'User' => 'User',
	'Group' => 'Group',
	'Yes' => 'Yes',
	'No' => 'No',
	'Lang_Not_Found' => 'Could not find language.',
	'Lang_Change' => 'Change the language',
	'File_Not_Found' => 'Could not find the file.',
	'File_Open_Edit_Not_Allowed' => 'You are not allowed to %s this file.', // %s = open or edit
	'Edit' => 'Edit',
	'Edit_File' => "Edit file's content",
	'File_Save_OK' => "File successfully saved.",
	'File_Save_Error' => "There was an error while saving the file.",
	'New_File' => 'New File',
	'No_Extension' => 'You have to add a file extension.',
	'Valid_Extensions' => 'Valid extensions: %s', // %s = txt,log etc.
	'Upload_message' => "Drop file here to upload",

	'SERVER ERROR' => "SERVER ERROR",
	'forbiden' => "Forbiden",
	'wrong path' => "Wrong path",
	'wrong name' => "Wrong name",
	'wrong extension' => "Wrong extension",
	'wrong option' => "Wrong option",
	'wrong data' => "Wrong data",
	'wrong action' => "Wrong action",
	'wrong sub-action' => "Wrong sub-actio",
	'no action passed' => "No action passed",
	'no path' => "No path",
	'no file' => "No file",
	'view type number missing' => "View type number missing",
	'Not enought Memory' => "Not enought Memory",
	'max_size_reached' => "Your image folder has reach its maximale size of %d MB.", //%d = max overall size
	'B' => "B",
	'KB' => "KB",
	'MB' => "MB",
	'GB' => "GB",
	'TB' => "TB",
	'total size' => "Total size",
);
