/.idea
!/.idea/php.xml
/app/*
!/app/code
!/app/design
!/app/etc
/app/etc/*
!app/i18n
app/i18n/*
!app/i18n/xortex
!/app/etc/config.php
!/app/etc/hyva-themes.json
/bin/*
!/bin/n98-magerun2
/db
/dev
/lib/web
/lib/.htaccess
/phpserver
/generated
/pub/*
!/pub/errors
/pub/errors/*
!/pub/errors/default
/setup
/var
/vendor
/.htaccess
/.htaccess.sample
/.php_cs
/.travis.yml
/.travis.yml.sample
/CHANGELOG.md
/CONTRIBUTING.md
/CONTRIBUTOR_LICENSE_AGREEMENT.html
/COPYING.txt
/SECURITY.md
/Gruntfile.js
/index.php
/LICENSE.txt
/LICENSE_AFL.txt
/nginx.conf.sample
/package.json
/php.ini.sample
/.php_cs.dist
/.php_cs.cache
/auth.json.sample
/grunt-config.json.sample
/Gruntfile.js.sample
/ISSUE_TEMPLATE.md
/package.json.sample
/PULL_REQUEST_TEMPLATE.md
/.user.ini
/.github
/config/etc/env.php
/lib/internal/GnuFreeFont
/lib/internal/LinLibertineFont
/pi.php
/README.md
/package-lock.json
/usage2/
/sitemap_de.xml
.editorconfig
/log/
/node_modules
/.php-cs-fixer.dist.php
/core
/app/design/frontend/Gfp/*/*/tailwind/node_modules
/app/design/frontend/Gfp/gfp/int/tailwind/node_modules
/app/design/frontend/Gfp/*/*/css/styles.css
/app/design/frontend/Gfp/*/*/tailwind/tailwind-output.css
/app/code/Rossmc/Partytown/view/frontend/web/js/node_modules
